# ShopMart E-Commerce Website - Dark Theme Guide

This guide explains the dark theme features added to your ShopMart e-commerce website and how to customize them.

## Dark Theme Features

Your e-commerce website now includes a sleek dark theme with the following features:

1. **Dark Background**: The entire website has a dark background (#121212) that's easy on the eyes
2. **Custom Card Styling**: All cards have dark backgrounds with subtle borders
3. **Custom Form Elements**: Form inputs, selects, and buttons are styled to match the dark theme
4. **Custom Table Styling**: Tables have dark backgrounds with proper contrast
5. **Custom Scrollbar**: A themed scrollbar that matches the dark design
6. **Animations**: Fade-in animations for a smoother user experience
7. **Consistent Color Scheme**: A cohesive color palette throughout the site

## How to Customize the Dark Theme

You can customize the dark theme by editing the `App.css` file in the `src` folder. Here are some key sections you might want to modify:

### Background Color

To change the main background color:

```css
body {
  background-color: #121212; /* Change this to your preferred color */
  color: #f8f9fa; /* Change this to your preferred text color */
}
```

### Card Styling

To change the card background and border:

```css
.card {
  background-color: #1e1e1e; /* Change this to your preferred card background */
  border: 1px solid #333; /* Change this to your preferred border color */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); /* Adjust shadow as needed */
}
```

### Button Colors

To change the primary button color:

```css
.btn-primary {
  background-color: #007bff; /* Change this to your preferred button color */
  border-color: #007bff; /* Match the border color */
}

.btn-primary:hover {
  background-color: #0069d9; /* A slightly darker shade for hover state */
  border-color: #0062cc; /* Match the border color */
}
```

## Pakistan-Specific Features

The website now includes Pakistan-specific features:

1. **Pakistan as Default Country**: Pakistan is now the default country in the checkout form
2. **Cash on Delivery**: Added Cash on Delivery as a payment option, which is popular in Pakistan
3. **Pakistan Address Format**: The order confirmation page shows an address format common in Pakistan

## Order Tracking Features

The enhanced order confirmation page now includes:

1. **Order Date and Time**: Shows exactly when the order was placed
2. **Estimated Delivery**: Calculates and displays the estimated delivery date
3. **Order Status Tracking**: Visual progress bar showing the current status of the order
4. **Detailed Order Summary**: Complete breakdown of the order with product images

## How to Run the Website

1. Open your terminal or command prompt
2. Navigate to the ecommerce-website directory:
   ```
   cd d:\HAMMAD\Ecommerce website\ecommerce-website
   ```
3. Install React Router DOM (if you haven't already):
   ```
   npm install react-router-dom
   ```
4. Start the development server:
   ```
   npm start
   ```

The application should automatically open in your default browser at http://localhost:3000 with the new dark theme applied.

## Troubleshooting

If the dark theme is not displaying correctly:

1. Make sure you've saved all the files after making changes
2. Clear your browser cache (Ctrl+F5 in most browsers)
3. Restart the development server
4. Check the browser console for any CSS errors

## Additional Customization

You can further customize the dark theme by:

1. Adding your own brand colors throughout the CSS
2. Creating custom dark-themed images that match the aesthetic
3. Adjusting the contrast and brightness for better readability
4. Adding more animations and transitions for a more dynamic experience

The dark theme is designed to be easy on the eyes while providing a modern, professional look for your e-commerce website.
