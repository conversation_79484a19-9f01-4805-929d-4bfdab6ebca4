# E-Commerce Website

A full-featured e-commerce website built with React.

## Features

This e-commerce website includes the following features:

### User Features

- **Product Browsing**: Browse products by category, search for products, and filter by price
- **Product Details**: View detailed product information, images, and related products
- **Shopping Cart**: Add products to cart, update quantities, and remove items
- **User Authentication**: Register, login, and manage user profile
- **Checkout Process**: Multi-step checkout with shipping, payment, and order review
- **Order Management**: View order history and track orders

### Technical Features

- React Router for navigation
- Context API for state management (Cart and Auth contexts)
- Responsive design with Bootstrap
- Form validation
- Local storage for cart persistence

## Installation

Before running this project, you need to install the required dependencies:

```bash
npm install react-router-dom
```

## Running the Project

In the project directory, you can run:

```bash
npm start
```

This runs the app in development mode. Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

## Project Structure

```
src/
├── components/       # Reusable UI components
├── context/          # Context providers for state management
├── data/             # Sample data for products and categories
├── pages/            # Page components for different routes
├── App.js            # Main application component with routing
└── index.js          # Entry point
```

## Demo Credentials

For testing the login functionality, you can use:

- Email: <EMAIL>
- Password: password

## Future Enhancements

- Backend integration with a real API
- Payment gateway integration
- Admin dashboard for product and order management
- User reviews and ratings
- Wishlist functionality
- Social media sharing
