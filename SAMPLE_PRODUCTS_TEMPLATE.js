// SAMPLE PRODUCTS TEMPLATE
// Copy and paste these product objects into your src/data/products.js file
// Make sure to:
// 1. Update the image paths to match your actual image locations
// 2. Adjust IDs to continue from your last product ID
// 3. Add commas between products in the array

// ============ FOOTWEAR PRODUCTS ============

{
  id: 9,
  name: "Sports Sneakers",
  price: 89.99,
  description: "Comfortable sports sneakers perfect for casual wear and light exercise.",
  image: require("../images/product images/footwear/sports-sneakers.jpg"), // Update path
  category: "Footwear",
  rating: 4.3,
  reviews: 78,
  inStock: true,
  features: [
    "Breathable mesh upper",
    "Cushioned insole",
    "Non-slip rubber outsole",
    "Lightweight design",
    "Available in multiple colors"
  ]
},
{
  id: 10,
  name: "Casual Loafers",
  price: 69.99,
  description: "Stylish and comfortable loafers for everyday casual wear.",
  image: require("../images/product images/footwear/casual-loafers.jpg"), // Update path
  category: "Footwear",
  rating: 4.1,
  reviews: 45,
  inStock: true,
  features: [
    "Genuine leather upper",
    "Slip-on design",
    "Padded footbed",
    "Flexible sole",
    "Classic style"
  ]
},
{
  id: 11,
  name: "Hiking Boots",
  price: 149.99,
  description: "Durable hiking boots designed for rough terrain and outdoor adventures.",
  image: require("../images/product images/footwear/hiking-boots.jpg"), // Update path
  category: "Footwear",
  rating: 4.7,
  reviews: 92,
  inStock: true,
  features: [
    "Waterproof construction",
    "Ankle support",
    "Rugged outsole for traction",
    "Cushioned midsole",
    "Breathable lining"
  ]
},
{
  id: 12,
  name: "Formal Dress Shoes",
  price: 119.99,
  description: "Elegant formal shoes perfect for business and special occasions.",
  image: require("../images/product images/footwear/formal-shoes.jpg"), // Update path
  category: "Footwear",
  rating: 4.5,
  reviews: 63,
  inStock: true,
  features: [
    "Genuine leather construction",
    "Classic design",
    "Comfortable insole",
    "Durable outsole",
    "Available in black and brown"
  ]
},

// ============ CLOTHING PRODUCTS ============

{
  id: 13,
  name: "Denim Jeans",
  price: 59.99,
  description: "Classic denim jeans with a comfortable fit and durable construction.",
  image: require("../images/product images/clothing/denim-jeans.jpg"), // Update path
  category: "Clothing",
  rating: 4.4,
  reviews: 87,
  inStock: true,
  features: [
    "100% cotton denim",
    "Classic 5-pocket design",
    "Straight leg fit",
    "Machine washable",
    "Available in multiple washes"
  ]
},
{
  id: 14,
  name: "Casual Hoodie",
  price: 45.99,
  description: "Comfortable hoodie perfect for casual wear and relaxation.",
  image: require("../images/product images/clothing/casual-hoodie.jpg"), // Update path
  category: "Clothing",
  rating: 4.2,
  reviews: 56,
  inStock: true,
  features: [
    "Soft cotton blend",
    "Kangaroo pocket",
    "Adjustable hood",
    "Ribbed cuffs and hem",
    "Available in multiple colors"
  ]
},
{
  id: 15,
  name: "Formal Shirt",
  price: 39.99,
  description: "Crisp formal shirt suitable for business and formal occasions.",
  image: require("../images/product images/clothing/formal-shirt.jpg"), // Update path
  category: "Clothing",
  rating: 4.3,
  reviews: 42,
  inStock: true,
  features: [
    "100% cotton",
    "Button-down collar",
    "Regular fit",
    "Easy care fabric",
    "Available in white, blue, and black"
  ]
},

// ============ HOME & KITCHEN PRODUCTS ============

{
  id: 16,
  name: "Blender",
  price: 79.99,
  description: "Powerful blender for smoothies, soups, and other culinary creations.",
  image: require("../images/product images/home-kitchen/blender.jpg"), // Update path
  category: "Home & Kitchen",
  rating: 4.5,
  reviews: 68,
  inStock: true,
  features: [
    "700-watt motor",
    "Multiple speed settings",
    "Pulse function",
    "Dishwasher-safe parts",
    "BPA-free materials"
  ]
},
{
  id: 17,
  name: "Toaster",
  price: 49.99,
  description: "2-slice toaster with adjustable browning control for perfect toast every time.",
  image: require("../images/product images/home-kitchen/toaster.jpg"), // Update path
  category: "Home & Kitchen",
  rating: 4.2,
  reviews: 53,
  inStock: true,
  features: [
    "6 browning settings",
    "Defrost function",
    "Reheat function",
    "Cancel button",
    "Easy-clean crumb tray"
  ]
},
{
  id: 18,
  name: "Cookware Set",
  price: 129.99,
  description: "Complete cookware set with pots and pans for all your cooking needs.",
  image: require("../images/product images/home-kitchen/cookware-set.jpg"), // Update path
  category: "Home & Kitchen",
  rating: 4.6,
  reviews: 75,
  inStock: true,
  features: [
    "10-piece set",
    "Non-stick coating",
    "Heat-resistant handles",
    "Dishwasher safe",
    "Compatible with all stovetops"
  ]
},

// ============ ACCESSORIES PRODUCTS ============

{
  id: 19,
  name: "Leather Wallet",
  price: 29.99,
  description: "Genuine leather wallet with multiple card slots and compartments.",
  image: require("../images/product images/accessories/leather-wallet.jpg"), // Update path
  category: "Accessories",
  rating: 4.4,
  reviews: 62,
  inStock: true,
  features: [
    "Genuine leather",
    "Multiple card slots",
    "Bill compartment",
    "ID window",
    "Slim design"
  ]
},
{
  id: 20,
  name: "Sunglasses",
  price: 39.99,
  description: "Stylish sunglasses with UV protection for sunny days.",
  image: require("../images/product images/accessories/sunglasses.jpg"), // Update path
  category: "Accessories",
  rating: 4.3,
  reviews: 48,
  inStock: true,
  features: [
    "UV400 protection",
    "Polarized lenses",
    "Durable frame",
    "Includes case",
    "Unisex design"
  ]
},
{
  id: 21,
  name: "Wristwatch",
  price: 89.99,
  description: "Elegant wristwatch with precise movement and stylish design.",
  image: require("../images/product images/accessories/wristwatch.jpg"), // Update path
  category: "Accessories",
  rating: 4.5,
  reviews: 57,
  inStock: true,
  features: [
    "Quartz movement",
    "Stainless steel case",
    "Leather strap",
    "Water resistant",
    "Date display"
  ]
},

// ============ BEAUTY & PERSONAL CARE PRODUCTS ============

{
  id: 22,
  name: "Facial Cleanser",
  price: 24.99,
  description: "Gentle facial cleanser that removes impurities without drying the skin.",
  image: require("../images/product images/beauty/facial-cleanser.jpg"), // Update path
  category: "Beauty & Personal Care",
  rating: 4.4,
  reviews: 83,
  inStock: true,
  features: [
    "Suitable for all skin types",
    "Fragrance-free",
    "Removes makeup",
    "pH balanced",
    "Dermatologist tested"
  ]
},
{
  id: 23,
  name: "Moisturizer",
  price: 29.99,
  description: "Hydrating moisturizer that nourishes and protects the skin.",
  image: require("../images/product images/beauty/moisturizer.jpg"), // Update path
  category: "Beauty & Personal Care",
  rating: 4.5,
  reviews: 76,
  inStock: true,
  features: [
    "24-hour hydration",
    "Non-greasy formula",
    "Contains SPF 15",
    "Anti-aging properties",
    "Suitable for daily use"
  ]
},
{
  id: 24,
  name: "Perfume",
  price: 59.99,
  description: "Elegant fragrance with long-lasting scent for any occasion.",
  image: require("../images/product images/beauty/perfume.jpg"), // Update path
  category: "Beauty & Personal Care",
  rating: 4.6,
  reviews: 92,
  inStock: true,
  features: [
    "Eau de parfum",
    "50ml bottle",
    "Long-lasting scent",
    "Floral and fruity notes",
    "Elegant glass bottle"
  ]
},
{
  id: 25,
  name: "Hair Styling Kit",
  price: 49.99,
  description: "Complete hair styling kit with multiple attachments for various styles.",
  image: require("../images/product images/beauty/hair-styling-kit.jpg"), // Update path
  category: "Beauty & Personal Care",
  rating: 4.3,
  reviews: 65,
  inStock: true,
  features: [
    "Multiple heat settings",
    "Various styling attachments",
    "Fast heat-up",
    "Auto shut-off",
    "Includes storage case"
  ]
}
