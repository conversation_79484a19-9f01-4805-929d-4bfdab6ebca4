# Using Placeholder Images for Your E-commerce Website

If you don't have all the product images ready yet, you can use placeholder images temporarily. This guide explains how to use placeholder images in your e-commerce website.

## Option 1: Using Online Placeholder Services

### Step 1: Update the Product Data

Open `src/data/products.js` and replace the image require statements with placeholder URLs:

```javascript
// Instead of:
image: require("../images/product images/footwear/sports-sneakers.jpg"),

// Use:
image: "https://via.placeholder.com/600x600?text=Sports+Sneakers",
```

### Example for Footwear Products

```javascript
{
  id: 9,
  name: "Sports Sneakers",
  price: 89.99,
  description: "Comfortable sports sneakers perfect for casual wear and light exercise.",
  image: "https://via.placeholder.com/600x600?text=Sports+Sneakers",
  category: "Footwear",
  rating: 4.3,
  reviews: 78,
  inStock: true,
  features: [
    "Breathable mesh upper",
    "Cushioned insole",
    "Non-slip rubber outsole",
    "Lightweight design",
    "Available in multiple colors"
  ]
}
```

### Placeholder Services

You can use these placeholder services:

1. **Placeholder.com**: `https://via.placeholder.com/600x600?text=Product+Name`
2. **PlaceImg**: `https://placeimg.com/600/600/tech` (for tech products)
3. **LoremPixel**: `https://lorempixel.com/600/600/fashion` (for fashion items)

## Option 2: Using Local Placeholder Images

### Step 1: Create a Placeholder Image

1. Create a simple placeholder image (600x600 pixels)
2. Save it as `placeholder.jpg` in `src/images/product images/`

### Step 2: Update the Product Data

```javascript
// For all products without images:
image: require("../images/product images/placeholder.jpg"),
```

## Option 3: Using the onError Fallback

Your product components already include fallbacks for missing images. If an image fails to load, a placeholder with the product name will be displayed instead.

```javascript
<img
  src={product.image}
  alt={product.name}
  onError={(e) => e.target.src = `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}`}
/>
```

## Customizing Placeholder Images

### Color and Style

You can customize placeholder images from placeholder.com:

- **Background color**: `https://via.placeholder.com/600x600/FF0000?text=Product+Name` (red background)
- **Text color**: `https://via.placeholder.com/600x600/FF0000/FFFFFF?text=Product+Name` (white text on red background)

### Category-Specific Placeholders

Create different placeholders for each category:

```javascript
// For footwear products
image: `https://via.placeholder.com/600x600/6A0DAD/FFFFFF?text=Footwear:+${encodeURIComponent(name)}`,

// For clothing products
image: `https://via.placeholder.com/600x600/4B0082/FFFFFF?text=Clothing:+${encodeURIComponent(name)}`,

// For electronics products
image: `https://via.placeholder.com/600x600/000000/FFFFFF?text=Electronics:+${encodeURIComponent(name)}`,
```

## Replacing Placeholders with Real Images

When you have the actual product images:

1. Place the images in the appropriate category folders
2. Update the product data to use the require statements:

```javascript
// Replace:
image: "https://via.placeholder.com/600x600?text=Sports+Sneakers",

// With:
image: require("../images/product images/footwear/sports-sneakers.jpg"),
```

## Best Practices

1. **Consistent Dimensions**: Keep all placeholder images at 600x600 pixels
2. **Descriptive Text**: Include the product name in the placeholder text
3. **Category Indication**: Consider including the category name in the placeholder
4. **Temporary Solution**: Remember that placeholders are temporary and should be replaced with real product images as soon as possible

By using placeholder images, you can develop and test your e-commerce website even before you have all the actual product images ready.
