INSTRUCTIONS FOR ADDING IMAGES TO YOUR SHOPMART E-COMMERCE WEBSITE

1. Place all your image files in this 'images' folder.

2. Required image files to add:
   - about-us.jpg (for the About page)
   - electronics.jpg (for the Categories page)
   - clothing.jpg (for the Categories page)
   - home.jpg (for the Categories page)
   - footwear.jpg (for the Categories page)
   - accessories.jpg (for the Categories page)
   - beauty.jpg (for the Categories page)
   - Product images (name them with product IDs, e.g., product-1.jpg, product-2.jpg, up to product-8.jpg)
   - hero-banner.jpg (for the homepage hero section)
   - special-offer.jpg (for the homepage special offers section)
   - new-arrivals.jpg (for the homepage new arrivals section)
   - customer-1.jpg, customer-2.jpg, customer-3.jpg (for customer testimonials)
   - Payment method icons: visa.png, mastercard.png, paypal.png, amex.png

3. Image size recommendations:
   - Product images: 600x600 pixels (square)
   - Hero banner: 1200x800 pixels
   - Category images: 600x400 pixels
   - Special offer/new arrivals: 800x400 pixels
   - About page images: 600x400 pixels
   - Customer testimonials: 80x80 pixels (square)
   - Payment icons: 60x40 pixels

4. Social Media Links:
   The footer includes links to the following social media platforms:
   - Facebook: https://facebook.com (update with your actual Facebook page URL)
   - WhatsApp: https://wa.me/1234567890 (update with your actual WhatsApp number)
   - LinkedIn: https://linkedin.com (update with your actual LinkedIn profile URL)
   - Instagram: https://instagram.com (update with your actual Instagram profile URL)

5. After adding images to this folder, they will automatically be used by the website.
   If any image is missing, the website will fall back to placeholder images.

6. To customize product images, update the product data in src/data/products.js:
   image: "/images/product-1.jpg"

7. To customize category images, update the category data in src/data/categories.js:
   image: "/images/electronics.jpg"

Note: The website has been designed with a beautiful UI that includes animations, hover effects, and a modern design. All images have fallback placeholders, so the website will look good even without adding your own images.
