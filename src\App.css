/* Black and Purple Theme Styles */
:root {
  --primary-color: #9c27b0;      /* Purple */
  --primary-dark: #7b1fa2;       /* Dark Purple */
  --primary-light: #ce93d8;      /* Light Purple */
  --accent-color: #ff4081;       /* Pink accent */
  --dark-bg: #121212;            /* Dark background */
  --darker-bg: #0a0a0a;          /* Darker background */
  --card-bg: #1e1e1e;            /* Card background */
  --text-primary: #f8f9fa;       /* Primary text */
  --text-secondary: #e0e0e0;     /* Secondary text */
  --text-muted: #bdbdbd;         /* Muted text */
  --border-color: #333;          /* Border color */
}

body {
  background-color: var(--dark-bg);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.App {
  min-height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-dark);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Card Styles */
.card {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease !important;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(156, 39, 176, 0.3) !important;
  border-color: var(--primary-color) !important;
}

.card-body {
  color: var(--text-primary);
  transition: background-color 0.3s ease;
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
}

.card-text {
  color: var(--text-secondary);
}

/* Product Card Hover Effect */
.product-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.product-card:hover::after {
  transform: scaleX(1);
}

/* Table Styles */
.table {
  color: var(--text-primary);
  border-collapse: separate;
  border-spacing: 0;
}

.table-light {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background-color: var(--darker-bg) !important;
  border-bottom: 1px solid var(--primary-dark) !important;
  color: var(--text-primary);
  padding: 1rem 1.25rem;
}

.table thead th {
  border-bottom-color: var(--primary-dark);
  color: var(--primary-light);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.table td, .table th {
  border-top-color: var(--border-color);
  padding: 1rem;
  vertical-align: middle;
}

.table tbody tr {
  transition: background-color 0.2s ease;
}

.table tbody tr:hover {
  background-color: rgba(156, 39, 176, 0.1);
}

/* Form Styles */
.form-control {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.form-control:focus {
  background-color: var(--darker-bg);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.25rem rgba(156, 39, 176, 0.25);
}

.form-select {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.form-select:focus {
  background-color: var(--darker-bg);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.25rem rgba(156, 39, 176, 0.25);
}

.form-control::placeholder,
.form-select::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

/* Button Styles */
.btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease !important;
  z-index: 1;
  border-radius: 0.375rem;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.5s ease, height 0.5s ease;
  z-index: -1;
}

.btn:hover::after {
  width: 300%;
  height: 300%;
}

.btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
  box-shadow: 0 0 0 0.25rem rgba(156, 39, 176, 0.25) !important;
}

.btn-outline-primary {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.btn-outline-secondary {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.btn-outline-secondary:hover {
  background-color: var(--card-bg) !important;
  color: var(--primary-light) !important;
  border-color: var(--primary-light) !important;
}

.btn-success {
  background: linear-gradient(45deg, #43a047, #66bb6a) !important;
  border: none !important;
}

.btn-danger {
  background: linear-gradient(45deg, #e53935, #f44336) !important;
  border: none !important;
}

.btn-info {
  background: linear-gradient(45deg, #039be5, #29b6f6) !important;
  border: none !important;
  color: white !important;
}

.btn-warning {
  background: linear-gradient(45deg, #fb8c00, #ffa726) !important;
  border: none !important;
  color: white !important;
}

/* Navbar Styles */
.navbar-dark {
  background-color: var(--darker-bg) !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.7);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--primary-dark);
}

.navbar-brand {
  font-weight: 700;
  letter-spacing: 0.5px;
}

.navbar-brand span {
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link {
  color: var(--text-secondary);
  position: relative;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover::after,
.navbar-dark .navbar-nav .nav-link.active::after {
  transform: scaleX(1);
  transform-origin: left;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
  color: var(--primary-light);
}

/* Footer Styles */
footer.bg-dark {
  background-color: var(--darker-bg) !important;
  border-top: 1px solid var(--primary-dark);
  position: relative;
  overflow: hidden;
}

footer.bg-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

footer h5.text-primary {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

footer h5.text-primary::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 2px;
  background: var(--primary-color);
}

/* Product Card Styles */
.product-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
}

.product-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 30px rgba(156, 39, 176, 0.2) !important;
  border-color: var(--primary-color);
  z-index: 2;
}

.product-card .card-img-top {
  transition: transform 0.6s ease;
}

.product-card:hover .card-img-top {
  transform: scale(1.1);
}

.product-card .product-overlay {
  background: rgba(10, 10, 10, 0.7);
  backdrop-filter: blur(3px);
  border-radius: 50px;
  padding: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.product-card:hover .product-overlay {
  transform: translateY(0);
  opacity: 1;
}

.product-card .btn-light {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.product-card .btn-light:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

/* Add to cart animation */
@keyframes addToCartAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.add-to-cart-animation {
  animation: addToCartAnimation 0.5s ease;
}

/* Category Card Styles */
.category-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(156, 39, 176, 0.2) !important;
  border-color: var(--primary-color);
}

.category-card .position-relative {
  overflow: hidden;
}

.category-card img {
  transition: transform 0.6s ease;
}

.category-card:hover img {
  transform: scale(1.1);
}

.category-card .category-overlay {
  background: linear-gradient(to top, rgba(10, 10, 10, 0.8), rgba(156, 39, 176, 0.4));
  backdrop-filter: blur(2px);
}

.category-card .btn-light {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  transition: all 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
}

.category-card:hover .btn-light {
  transform: translateY(0);
  opacity: 1;
}

.category-card .btn-light:hover {
  background: var(--primary-color);
  color: white;
}

.category-card .card-title {
  position: relative;
  display: inline-block;
  transition: color 0.3s ease;
}

.category-card:hover .card-title {
  color: var(--primary-color);
}

.category-card .card-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.category-card:hover .card-title::after {
  width: 50%;
}

/* Alert Styles */
.alert {
  border: none !important;
  border-radius: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  padding-left: 1.5rem;
}

.alert::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 5px;
}

.alert-info {
  background-color: rgba(13, 58, 88, 0.9) !important;
  color: #d1ecf1 !important;
}

.alert-info::before {
  background: linear-gradient(to bottom, #039be5, #29b6f6);
}

.alert-success {
  background-color: rgba(13, 70, 46, 0.9) !important;
  color: #d1e7dd !important;
}

.alert-success::before {
  background: linear-gradient(to bottom, #43a047, #66bb6a);
}

.alert-danger {
  background-color: rgba(88, 13, 13, 0.9) !important;
  color: #f8d7da !important;
}

.alert-danger::before {
  background: linear-gradient(to bottom, #e53935, #f44336);
}

.alert-warning {
  background-color: rgba(88, 58, 13, 0.9) !important;
  color: #fff3cd !important;
}

.alert-warning::before {
  background: linear-gradient(to bottom, #fb8c00, #ffa726);
}

.bg-success {
  background-color: #43a047 !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-danger {
  background-color: #e53935 !important;
}

/* Badge Styles */
.badge {
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 0.5em 0.8em;
  border-radius: 50rem;
  transition: all 0.3s ease;
}

.badge.bg-light {
  background-color: rgba(44, 44, 44, 0.8) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color);
}

.badge.bg-primary {
  background: linear-gradient(45deg, var(--primary-dark), var(--primary-color)) !important;
}

.badge.bg-danger {
  background: linear-gradient(45deg, #d32f2f, #f44336) !important;
}

.badge.bg-success {
  background: linear-gradient(45deg, #388e3c, #4caf50) !important;
}

.badge.bg-warning {
  background: linear-gradient(45deg, #f57c00, #ffa000) !important;
  color: white !important;
}

.badge.bg-info {
  background: linear-gradient(45deg, #0288d1, #29b6f6) !important;
  color: white !important;
}

/* Progress Bar Styles */
.progress {
  background-color: var(--darker-bg);
  height: 0.8rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-bar {
  background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
  position: relative;
  overflow: hidden;
  transition: width 0.6s ease;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Text Styles */
.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Glassmorphism */
.glass-effect {
  background: rgba(30, 30, 30, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}
