import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { CartContext } from '../context/CartContext';

const Navbar = () => {
  const { cartItems } = useContext(CartContext);

  // Calculate total items in cart
  const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-dark">
      <div className="container">
        <Link className="navbar-brand" to="/">
          Shop<span>Mart</span>
        </Link>
        <button className="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span className="navbar-toggler-icon"></span>
        </button>
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <Link className="nav-link" to="/">
                <i className="fas fa-home me-1"></i> Home
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/products">
                <i className="fas fa-box me-1"></i> Products
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/categories">
                <i className="fas fa-th-large me-1"></i> Categories
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/about">
                <i className="fas fa-info-circle me-1"></i> About
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/contact">
                <i className="fas fa-envelope me-1"></i> Contact
              </Link>
            </li>
          </ul>
          <div className="d-flex">
            <Link to="/wishlist" className="btn btn-outline-primary me-2 hover-lift">
              <i className="fas fa-heart"></i>
            </Link>
            <Link to="/cart" className="btn btn-outline-primary me-2 position-relative hover-lift">
              <i className="fas fa-shopping-cart"></i>
              {totalItems > 0 && (
                <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger pulse">
                  {totalItems}
                </span>
              )}
            </Link>
            <Link to="/orders" className="btn btn-outline-primary me-2 hover-lift">
              <i className="fas fa-box me-1"></i> Orders
            </Link>
            <Link to="/login" className="btn btn-primary hover-lift">
              <i className="fas fa-user me-1"></i> Login
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
