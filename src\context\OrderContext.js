import React, { createContext, useState } from 'react';

export const OrderContext = createContext();

export const OrderProvider = ({ children }) => {
  const [orderInfo, setOrderInfo] = useState({
    orderNumber: null,
    orderDate: null,
    customerInfo: {
      firstName: '',
      lastName: '',
      email: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: ''
    },
    paymentMethod: '',
    cardDetails: {
      cardNumber: '',
    },
    items: [],
    subtotal: 0,
    shipping: 0,
    tax: 0,
    total: 0
  });

  const createOrder = (orderData) => {
    // Generate a random order number
    const orderNumber = Math.floor(100000 + Math.random() * 900000);
    
    // Set the order date to current date and time
    const orderDate = new Date();
    
    // Calculate estimated delivery date (5-7 days from now)
    const estimatedDelivery = new Date(orderDate);
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 5 + Math.floor(Math.random() * 3));
    
    // Calculate order totals
    const subtotal = orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = subtotal > 100 ? 0 : 10;
    const tax = subtotal * 0.08;
    const total = subtotal + shipping + tax;
    
    // Create the complete order object
    const newOrder = {
      orderNumber,
      orderDate,
      estimatedDelivery,
      customerInfo: {
        firstName: orderData.firstName,
        lastName: orderData.lastName,
        email: orderData.email,
        address: orderData.address,
        city: orderData.city,
        state: orderData.state,
        zipCode: orderData.zipCode,
        country: orderData.country
      },
      paymentMethod: orderData.paymentMethod,
      cardDetails: {
        cardNumber: orderData.cardNumber || '',
      },
      items: orderData.items,
      subtotal,
      shipping,
      tax,
      total
    };
    
    setOrderInfo(newOrder);
    return newOrder;
  };

  return (
    <OrderContext.Provider value={{ orderInfo, createOrder }}>
      {children}
    </OrderContext.Provider>
  );
};
