// Sample product data for the e-commerce website
const products = [
  {
    id: 1,
    name: "Smartphone X",
    price: 799.99,
    description: "Latest smartphone with advanced features and high-resolution camera.",
    image: require("../images/product images/electronics/product-1.jpg"),
    category: "Electronics",
    rating: 4.5,
    reviews: 120,
    inStock: true,
    features: [
      "6.5-inch OLED display",
      "Triple camera system",
      "5G connectivity",
      "All-day battery life",
      "Water and dust resistant"
    ]
  },
  {
    id: 2,
    name: "Laptop Pro",
    price: 1299.99,
    description: "Powerful laptop for professionals and creative work.",
    image: require("../images/product images/electronics/product-2.jpg"),
    category: "Electronics",
    rating: 4.8,
    reviews: 85,
    inStock: true,
    features: [
      "15-inch Retina display",
      "16GB RAM",
      "512GB SSD storage",
      "10-hour battery life",
      "Backlit keyboard"
    ]
  },
  {
    id: 3,
    name: "Wireless Headphones",
    price: 199.99,
    description: "Premium wireless headphones with noise cancellation.",
    image: require("../images/product images/electronics/product-3.JPG"),
    category: "Electronics",
    rating: 4.6,
    reviews: 210,
    inStock: true,
    features: [
      "Active noise cancellation",
      "30-hour battery life",
      "Comfortable over-ear design",
      "High-quality sound",
      "Voice assistant support"
    ]
  },
  {
    id: 4,
    name: "Smart Watch",
    price: 249.99,
    description: "Feature-packed smartwatch for fitness and everyday use.",
    image: require("../images/product images/electronics/product-4.JPG"),
    category: "Electronics",
    rating: 4.3,
    reviews: 95,
    inStock: true,
    features: [
      "Heart rate monitoring",
      "GPS tracking",
      "Water resistant",
      "Sleep tracking",
      "7-day battery life"
    ]
  },
  {
    id: 5,
    name: "Designer T-Shirt",
    price: 49.99,
    description: "Premium cotton t-shirt with modern design.",
    image: require("../images/product images/clothing/product-5.JPG"),
    category: "Clothing",
    rating: 4.2,
    reviews: 65,
    inStock: true,
    features: [
      "100% organic cotton",
      "Comfortable fit",
      "Machine washable",
      "Available in multiple colors",
      "Sizes S to XXL"
    ]
  },
  {
    id: 6,
    name: "Running Shoes",
    price: 129.99,
    description: "Lightweight and comfortable running shoes for athletes.",
    image: require("../images/product images/footwear/product-6.jpg"),
    category: "Footwear",
    rating: 4.7,
    reviews: 180,
    inStock: true,
    features: [
      "Breathable mesh upper",
      "Responsive cushioning",
      "Durable rubber outsole",
      "Reflective details",
      "Available in multiple colors"
    ]
  },
  {
    id: 7,
    name: "Coffee Maker",
    price: 89.99,
    description: "Programmable coffee maker for perfect brewing every time.",
    image: require("../images/product images/home-kitchen/product-7.jpg"),
    category: "Home & Kitchen",
    rating: 4.4,
    reviews: 75,
    inStock: true,
    features: [
      "12-cup capacity",
      "Programmable timer",
      "Auto shut-off",
      "Brew strength control",
      "Easy to clean"
    ]
  },
  {
    id: 8,
    name: "Backpack",
    price: 59.99,
    description: "Durable backpack with multiple compartments for everyday use.",
    image: require("../images/product images/accessories/product-8.jpg"),
    category: "Accessories",
    rating: 4.5,
    reviews: 110,
    inStock: true,
    features: [
      "Water-resistant material",
      "Laptop compartment",
      "Multiple pockets",
      "Padded shoulder straps",
      "Breathable back panel"
    ]
  }
];

export default products;
