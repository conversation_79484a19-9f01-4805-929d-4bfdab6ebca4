import React from 'react';
import { useParams } from 'react-router-dom';

const AboutPage = () => {
  const { pageType } = useParams();

  // Determine which page content to show based on the URL parameter
  const getPageContent = () => {
    switch (pageType) {
      case 'categories':
        return {
          title: 'Product Categories',
          content: (
            <>
              <p className="lead text-primary fw-bold">
                At ShopMart, we offer a wide range of high-quality products across various categories to meet all your shopping needs.
              </p>
              <p className="mb-5">
                Our carefully curated collections feature premium products from top brands and manufacturers. We pride ourselves on offering excellent quality, competitive prices, and exceptional customer service. Browse our categories below to find exactly what you're looking for.
              </p>

              <div className="row mb-4">
                <div className="col-12">
                  <div className="bg-light p-4 rounded-3 shadow-sm">
                    <h4 className="text-primary mb-3">Why Shop by Category?</h4>
                    <ul className="list-unstyled">
                      <li className="mb-2"><i className="fas fa-check-circle text-success me-2"></i> Find exactly what you need quickly and easily</li>
                      <li className="mb-2"><i className="fas fa-check-circle text-success me-2"></i> Discover new products within your favorite categories</li>
                      <li className="mb-2"><i className="fas fa-check-circle text-success me-2"></i> Compare similar items to make informed purchasing decisions</li>
                      <li className="mb-2"><i className="fas fa-check-circle text-success me-2"></i> Enjoy category-specific promotions and discounts</li>
                    </ul>
                  </div>
                </div>
              </div>

              <h3 className="text-center mb-4 mt-5">Our Product Categories</h3>
              <div className="row">
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/product images/product-3.JPG")} className="card-img-top" alt="Electronics"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Electronics'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Electronics</h5>
                      <p className="card-text">Discover the latest gadgets and electronic devices for your home and office. From smartphones and laptops to smart home devices and accessories.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">42 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/product images/product-5.JPG")} className="card-img-top" alt="Clothing"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Clothing'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Clothing</h5>
                      <p className="card-text">Stylish apparel for men, women, and children at affordable prices. Stay on trend with our latest fashion collections for all seasons.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">56 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/product images/product-7.jpg")} className="card-img-top" alt="Home & Kitchen"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Home+%26+Kitchen'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Home & Kitchen</h5>
                      <p className="card-text">Everything you need to make your house a home. From furniture and decor to kitchen appliances and household essentials.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">38 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row mt-4">
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/product images/product-6.jpg")} className="card-img-top" alt="Footwear"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Footwear'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Footwear</h5>
                      <p className="card-text">Step out in style with our collection of shoes, sneakers, and boots for all occasions. Comfort and fashion for every step you take.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">29 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/product images/product-8.jpg")} className="card-img-top" alt="Accessories"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Accessories'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Accessories</h5>
                      <p className="card-text">Complete your look with our range of bags, watches, jewelry, and other accessories. Find the perfect finishing touch for any outfit.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">45 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-md-4 mb-4">
                  <div className="card h-100 shadow-sm category-card">
                    <div className="position-relative">
                      <img src={require("../images/category images/beauty.jpg")} className="card-img-top" alt="Beauty & Personal Care"
                           onError={(e) => e.target.src = 'https://via.placeholder.com/300x200?text=Beauty+%26+Personal+Care'} />
                      <div className="category-overlay">
                        <a href="/products" className="btn btn-light">Shop Now</a>
                      </div>
                    </div>
                    <div className="card-body">
                      <h5 className="card-title text-primary">Beauty & Personal Care</h5>
                      <p className="card-text">Discover skincare, makeup, and personal care products to help you look and feel your best. Quality brands for your beauty routine.</p>
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <span className="badge bg-primary">33 Products</span>
                        <a href="/products" className="btn btn-sm btn-outline-primary">View All</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <style jsx="true">{`
                .category-card {
                  transition: all 0.3s ease;
                  overflow: hidden;
                }
                .category-card:hover {
                  transform: translateY(-5px);
                  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
                }
                .category-overlay {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background-color: rgba(0,0,0,0.5);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  opacity: 0;
                  transition: all 0.3s ease;
                }
                .category-card:hover .category-overlay {
                  opacity: 1;
                }
              `}</style>
            </>
          )
        };
      case 'contact':
        return {
          title: 'Contact Us',
          content: (
            <>
              <p className="lead">
                We'd love to hear from you! Please use the form below to get in touch with our team.
              </p>
              <div className="row mt-5">
                <div className="col-md-6">
                  <form className="contact-form shadow-sm p-4 rounded bg-dark">
                    <h5 className="text-primary mb-4 border-bottom pb-2">Send Us a Message</h5>
                    <div className="mb-3">
                      <label htmlFor="name" className="form-label text-light">Your Name</label>
                      <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-user"></i></span>
                        <input type="text" className="form-control bg-dark text-light border-secondary" id="name" placeholder="Enter your name" required />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="email" className="form-label text-light">Email Address</label>
                      <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-envelope"></i></span>
                        <input type="email" className="form-control bg-dark text-light border-secondary" id="email" placeholder="Enter your email" required />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="phone" className="form-label text-light">Phone Number (Optional)</label>
                      <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-phone"></i></span>
                        <input type="tel" className="form-control bg-dark text-light border-secondary" id="phone" placeholder="Enter your phone number" />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="subject" className="form-label text-light">Subject</label>
                      <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-tag"></i></span>
                        <input type="text" className="form-control bg-dark text-light border-secondary" id="subject" placeholder="Enter subject" required />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="message" className="form-label text-light">Message</label>
                      <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-comment"></i></span>
                        <textarea className="form-control bg-dark text-light border-secondary" id="message" rows="5" placeholder="Enter your message" required></textarea>
                      </div>
                    </div>
                    <div className="mb-3 form-check">
                      <input type="checkbox" className="form-check-input" id="privacy" required />
                      <label className="form-check-label text-light" htmlFor="privacy">I agree to the privacy policy</label>
                    </div>
                    <button type="submit" className="btn btn-primary w-100">
                      <i className="fas fa-paper-plane me-2"></i>Send Message
                    </button>
                  </form>
                </div>
                <div className="col-md-6">
                  <div className="card">
                    <div className="card-body">
                      <h5 className="card-title">Our Information</h5>
                      <p className="card-text">
                        <strong>Address:</strong><br />
                        123 Commerce Street<br />
                        Shopping District<br />
                        Retail City, RC 12345
                      </p>
                      <p className="card-text">
                        <strong>Email:</strong><br />
                        <EMAIL>
                      </p>
                      <p className="card-text">
                        <strong>Phone:</strong><br />
                        <a href="tel:+923266396757" className="text-decoration-none">0326-6396757</a>
                      </p>
                      <p className="card-text">
                        <strong>Hours:</strong><br />
                        Monday - Friday: 9:00 AM - 6:00 PM<br />
                        Saturday: 10:00 AM - 4:00 PM<br />
                        Sunday: Closed
                      </p>
                      <div className="mt-4">
                        <h6 className="mb-3"><strong>Connect With Us:</strong></h6>
                        <div className="d-flex gap-3 fs-4">
                          <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-primary p-2 rounded-circle"><i className="fab fa-facebook-f"></i></a>
                          <a href="https://wa.me/923266396757" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-success p-2 rounded-circle"><i className="fab fa-whatsapp"></i></a>
                          <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-info p-2 rounded-circle"><i className="fab fa-linkedin-in"></i></a>
                          <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-danger p-2 rounded-circle"><i className="fab fa-instagram"></i></a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )
        };
      default: // About page
        return {
          title: 'About ShopMart',
          content: (
            <>
              <p className="lead fw-bold text-primary">
                Welcome to ShopMart – Where Quality Meets Affordability!
              </p>
              <div className="row mt-5">
                <div className="col-md-6">
                  <h3 className="text-primary mb-4">Our Story</h3>
                  <p className="mb-4">
                    Founded in 2023 with a vision to revolutionize online shopping, ShopMart has quickly emerged as a premier destination for discerning shoppers. We believe that everyone deserves access to premium products without the premium price tag.
                  </p>
                  <p className="mb-4">
                    What sets ShopMart apart is our unwavering commitment to quality, customer satisfaction, and innovation. Every product in our carefully curated collection undergoes rigorous quality checks to ensure you receive nothing but the best.
                  </p>
                  <p>
                    Our passionate team works tirelessly to bring you the latest trends, timeless classics, and innovative products that enhance your lifestyle. From cutting-edge electronics to fashion-forward apparel, we've got everything you need under one virtual roof.
                  </p>
                </div>
                <div className="col-md-6">
                  <div className="position-relative">
                    <img
                      src={require("../images/banner images/about us.jpeg")}
                      className="img-fluid rounded shadow"
                      alt="About ShopMart"
                      onError={(e) => e.target.src = 'https://via.placeholder.com/600x400?text=About+ShopMart'}
                      style={{border: '5px solid #fff'}}
                    />
                    <div className="position-absolute top-0 end-0 bg-primary text-white p-3 rounded-circle shadow-lg" style={{transform: 'translate(20%, -20%)'}}>
                      <h4 className="m-0">Est.<br/>2023</h4>
                    </div>
                  </div>
                  <div className="bg-dark p-4 mt-4 rounded shadow-sm">
                    <h5 className="text-primary">Our Promise</h5>
                    <p className="text-light mb-0">
                      "We don't just sell products; we deliver experiences, satisfaction, and value with every purchase."
                    </p>
                  </div>
                </div>
              </div>
              <div className="row mt-5 py-5 bg-dark rounded-3">
                <div className="col-md-12 text-center mb-4">
                  <h3 className="display-5 fw-bold text-primary">Our Core Values</h3>
                  <p className="lead text-light">The principles that guide everything we do</p>
                </div>
                <div className="col-md-12">
                  <div className="row mt-3">
                    <div className="col-md-4 mb-4">
                      <div className="card h-100 border-0 shadow-sm hover-shadow transition">
                        <div className="card-body text-center p-4">
                          <div className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-4" style={{width: '80px', height: '80px'}}>
                            <i className="fas fa-award fa-2x"></i>
                          </div>
                          <h5 className="card-title fw-bold">Exceptional Quality</h5>
                          <p className="card-text">We never compromise on the quality of our products. Each item in our inventory is carefully selected and rigorously quality-checked to ensure it meets our high standards.</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4 mb-4">
                      <div className="card h-100 border-0 shadow-sm">
                        <div className="card-body text-center p-4">
                          <div className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-4" style={{width: '80px', height: '80px'}}>
                            <i className="fas fa-heart fa-2x"></i>
                          </div>
                          <h5 className="card-title fw-bold">Customer Satisfaction</h5>
                          <p className="card-text">Your satisfaction is our top priority. We strive to provide an exceptional shopping experience from browsing to delivery, with responsive customer service every step of the way.</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4 mb-4">
                      <div className="card h-100 border-0 shadow-sm">
                        <div className="card-body text-center p-4">
                          <div className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-4" style={{width: '80px', height: '80px'}}>
                            <i className="fas fa-lightbulb fa-2x"></i>
                          </div>
                          <h5 className="card-title fw-bold">Continuous Innovation</h5>
                          <p className="card-text">We continuously improve our platform, products, and services to meet the evolving needs of our customers, always staying ahead of trends and embracing new technologies.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="row mt-5">
                <div className="col-12 text-center">
                  <h3 className="text-primary mb-4">Join the ShopMart Family Today!</h3>
                  <p className="lead mb-4">Experience the perfect blend of quality, affordability, and exceptional service.</p>
                  <a href="/products" className="btn btn-primary btn-lg px-5">Start Shopping</a>
                </div>
              </div>
            </>
          )
        };
    }
  };

  const pageContent = getPageContent();

  return (
    <div className="container py-5">
      <h1 className="mb-4">{pageContent.title}</h1>
      {pageContent.content}
    </div>
  );
};

export default AboutPage;
