import React, { useMemo, useContext } from 'react';
import { Link } from 'react-router-dom';
import products from '../data/products';
import categories from '../data/categories';
import { CartContext } from '../context/CartContext';

const HomePage = () => {
  // Get cart context
  const { addToCart } = useContext(CartContext);

  // Get featured products (first 4)
  const featuredProducts = products.slice(0, 4);

  // Calculate product counts by category
  const productCountsByCategory = useMemo(() => {
    const counts = {};
    categories.forEach(category => {
      counts[category.name] = products.filter(product => product.category === category.name).length;
    });
    return counts;
  }, []);

  // Handle add to cart with animation
  const handleAddToCart = (e, product) => {
    // Add the product to cart
    addToCart(product, 1);

    // Store a reference to the button element
    const button = e.currentTarget;

    // Add animation class
    if (button && button.classList) {
      button.classList.add('add-to-cart-animation');

      // Remove animation class after animation completes
      setTimeout(() => {
        // Check if the element still exists in the DOM
        if (button && button.classList && document.body.contains(button)) {
          button.classList.remove('add-to-cart-animation');
        }
      }, 500);
    }

    // Show a toast notification
    alert(`${product.name} added to cart!`);
  };

  return (
    <div>
      {/* Hero Section */}
      <div className="bg-dark text-white py-5 position-relative overflow-hidden">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-md-6 position-relative" style={{zIndex: 2}}>
              <span className="badge bg-danger mb-3 slide-in-left">Premium Quality</span>
              <h1 className="display-3 fw-bold mb-4 fade-in">Welcome to <span className="gradient-text">ShopMart</span></h1>
              <p className="lead fs-4 mb-4 slide-in-right">Discover exceptional products that enhance your lifestyle at prices that won't break the bank.</p>
              <div className="d-flex gap-3 mb-4 fade-in" style={{animationDelay: '0.3s'}}>
                <Link to="/products" className="btn btn-primary btn-lg px-4 py-3 shadow-lg hover-lift">
                  <i className="fas fa-shopping-cart me-2"></i>Shop Now
                </Link>
                <Link to="/categories" className="btn btn-outline-light btn-lg px-4 py-3 hover-lift">
                  <i className="fas fa-th-large me-2"></i>Categories
                </Link>
              </div>
              <div className="d-flex align-items-center">
                <div className="d-flex">
                  <img src={require("../images/other images/customer-1.jpg")} className="rounded-circle border border-3 border-primary" width="40" height="40" alt="Customer"
                       onError={(e) => e.target.src = "https://via.placeholder.com/40x40"} style={{marginRight: '-10px'}} />
                  <img src={require("../images/other images/customer-2.jpg")} className="rounded-circle border border-3 border-primary" width="40" height="40" alt="Customer"
                       onError={(e) => e.target.src = "https://via.placeholder.com/40x40"} style={{marginRight: '-10px'}} />
                  <img src={require("../images/other images/customer-3.png")} className="rounded-circle border border-3 border-primary" width="40" height="40" alt="Customer"
                       onError={(e) => e.target.src = "https://via.placeholder.com/40x40"} />
                </div>
                <div className="ms-3">
                  <p className="mb-0 fw-bold">Trusted by 10,000+ customers</p>
                  <div className="text-warning">
                    <i className="fas fa-star"></i>
                    <i className="fas fa-star"></i>
                    <i className="fas fa-star"></i>
                    <i className="fas fa-star"></i>
                    <i className="fas fa-star-half-alt"></i>
                    <span className="text-white ms-1">4.8/5</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 position-relative" style={{zIndex: 1}}>
              <div className="position-relative zoom-in">
                <img
                  src={require("../images/banner images/hero banner.jpg")}
                  alt="Hero"
                  className="img-fluid rounded shadow-lg"
                  style={{
                    border: '10px solid rgba(156, 39, 176, 0.2)',
                    transition: 'transform 0.5s ease',
                    transform: 'perspective(1000px) rotateY(-5deg)',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4)'
                  }}
                  onError={(e) => e.target.src = "https://via.placeholder.com/600x400?text=ShopMart"}
                />
                <div className="position-absolute top-0 start-0 translate-middle bg-primary text-white p-3 rounded-circle shadow-lg pulse">
                  <div className="fw-bold">SALE</div>
                  <div className="fs-4">50%</div>
                  <div>OFF</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="position-absolute top-0 end-0 bg-primary opacity-10" style={{width: '300px', height: '300px', borderRadius: '50%', transform: 'translate(100px, -150px)'}}></div>
        <div className="position-absolute bottom-0 start-0 bg-primary opacity-10" style={{width: '200px', height: '200px', borderRadius: '50%', transform: 'translate(-100px, 50px)'}}></div>
      </div>

      {/* Featured Products */}
      <div className="py-5">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-6 mx-auto text-center">
              <span className="badge bg-primary text-white mb-2 slide-in-left">Top Picks</span>
              <h2 className="display-5 fw-bold mb-3 fade-in">Featured <span className="gradient-text">Products</span></h2>
              <p className="text-muted slide-in-right">Discover our handpicked selection of premium products that combine quality, style, and value.</p>
            </div>
          </div>
          <div className="row">
            {featuredProducts.map(product => (
              <div key={product.id} className="col-md-3 mb-4">
                <div className="card h-100 border-0 shadow-sm product-card">
                  <div className="position-relative">
                    <div style={{height: '200px', overflow: 'hidden'}}>
                      <img
                        src={product.image}
                        className="card-img-top"
                        alt={product.name}
                        style={{objectFit: 'cover', height: '100%', width: '100%'}}
                        onError={(e) => e.target.src = `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}`}
                      />
                    </div>
                    {product.id % 2 === 0 && (
                      <span className="position-absolute top-0 start-0 bg-danger text-white py-1 px-2 m-2 rounded-pill">Sale</span>
                    )}
                    <div className="product-overlay">
                      <Link to={`/products/${product.id}`} className="btn btn-light btn-sm rounded-circle me-2">
                        <i className="fas fa-eye"></i>
                      </Link>
                      <button className="btn btn-light btn-sm rounded-circle">
                        <i className="fas fa-heart"></i>
                      </button>
                    </div>
                  </div>
                  <div className="card-body p-4">
                    <div className="text-warning mb-2">
                      {Array(Math.floor(product.rating)).fill().map((_, i) => (
                        <i key={i} className="fas fa-star"></i>
                      ))}
                      {product.rating % 1 !== 0 && <i className="fas fa-star-half-alt"></i>}
                    </div>
                    <h5 className="card-title fw-bold">{product.name}</h5>
                    <p className="card-text text-muted mb-0">{product.category}</p>
                    <div className="d-flex justify-content-between align-items-center mt-3">
                      <h5 className="text-primary mb-0 fw-bold">${product.price.toFixed(2)}</h5>
                      <button
                        className="btn btn-primary rounded-circle"
                        onClick={(e) => handleAddToCart(e, product)}
                      >
                        <i className="fas fa-shopping-cart"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-5">
            <Link to="/products" className="btn btn-outline-primary btn-lg px-5">
              <i className="fas fa-th-list me-2"></i>View All Products
            </Link>
          </div>
        </div>
      </div>

      {/* Custom CSS for product cards */}
      <style jsx="true">{`
        .product-card {
          transition: all 0.3s ease;
          overflow: hidden;
        }
        .product-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
        }
        .product-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          opacity: 0;
          transition: all 0.3s ease;
          display: flex;
        }
        .product-card:hover .product-overlay {
          opacity: 1;
        }
        .add-to-cart-animation {
          animation: pulse 0.5s ease;
        }
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.2); }
          100% { transform: scale(1); }
        }
      `}</style>

      {/* Categories */}
      <div className="py-5 bg-light">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-6 mx-auto text-center">
              <span className="badge bg-primary text-white mb-2 slide-in-left">Collections</span>
              <h2 className="display-5 fw-bold mb-3 fade-in">Shop by <span className="gradient-text">Category</span></h2>
              <p className="text-muted slide-in-right">Explore our wide range of product categories designed to meet all your needs</p>
            </div>
          </div>
          <div className="row">
            {categories.map(category => (
              <div key={category.id} className="col-md-4 mb-4">
                <div className="card border-0 shadow-sm category-card h-100">
                  <div className="position-relative overflow-hidden" style={{height: '200px'}}>
                    <img
                      src={category.image}
                      className="card-img-top h-100 w-100"
                      style={{objectFit: 'cover', objectPosition: 'center'}}
                      alt={category.name}
                      onError={(e) => e.target.src = `https://via.placeholder.com/300x200?text=${encodeURIComponent(category.name)}`}
                    />
                    <div className="category-overlay d-flex align-items-center justify-content-center">
                      <Link to={`/categories/${category.id}`} className="btn btn-light btn-lg">
                        <i className="fas fa-arrow-right"></i>
                      </Link>
                    </div>
                  </div>
                  <div className="card-body text-center p-4">
                    <h4 className="card-title fw-bold text-primary">{category.name}</h4>
                    <p className="card-text text-muted">{category.description}</p>
                    <Link to={`/categories/${category.id}`} className="btn btn-outline-primary mt-2">
                      Browse Products <i className="fas fa-angle-right ms-2"></i>
                    </Link>
                  </div>
                  <div className="card-footer bg-transparent border-0 text-center pb-4">
                    <span className="badge bg-light text-dark px-3 py-2 rounded-pill">
                      {productCountsByCategory[category.name]} Products
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom CSS for category cards */}
      <style jsx="true">{`
        .category-card {
          transition: all 0.3s ease;
          border-radius: 10px;
        }
        .category-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
        }
        .category-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0,0,0,0.3);
          opacity: 0;
          transition: all 0.3s ease;
        }
        .category-card:hover .category-overlay {
          opacity: 1;
        }
      `}</style>

      {/* Special Offers */}
      <div className="py-5">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-6 mx-auto text-center">
              <span className="badge bg-danger text-white mb-2 slide-in-left">Limited Time</span>
              <h2 className="display-5 fw-bold mb-3 fade-in">Special <span className="gradient-text">Offers</span></h2>
              <p className="text-muted slide-in-right">Don't miss out on these amazing deals and new arrivals</p>
            </div>
          </div>
          <div className="row">
            <div className="col-md-6 mb-4">
              <div className="card border-0 shadow-lg rounded-lg overflow-hidden offer-card">
                <div className="position-relative">
                  <img
                    src={require("../images/banner images/specialoffer.webp")}
                    className="card-img"
                    alt="Special Offer"
                    style={{height: '300px', objectFit: 'cover', objectPosition: 'center'}}
                    onError={(e) => e.target.src = "https://via.placeholder.com/600x300?text=Special+Offer"}
                  />
                  <div className="position-absolute top-0 start-0 w-100 h-100 bg-dark" style={{opacity: '0.4'}}></div>
                  <div className="card-img-overlay d-flex flex-column justify-content-center text-center">
                    <div className="bg-danger text-white d-inline-block py-2 px-4 rounded-pill mx-auto mb-3">Limited Time Offer</div>
                    <h3 className="card-title display-6 fw-bold text-white mb-3">Summer Sale</h3>
                    <p className="card-text text-white fs-5 mb-4">Get up to 50% off on selected items</p>
                    <div className="countdown d-flex justify-content-center gap-3 mb-4">
                      <div className="bg-white text-dark rounded p-2 px-3">
                        <div className="fs-4 fw-bold">02</div>
                        <div className="small">Days</div>
                      </div>
                      <div className="bg-white text-dark rounded p-2 px-3">
                        <div className="fs-4 fw-bold">12</div>
                        <div className="small">Hours</div>
                      </div>
                      <div className="bg-white text-dark rounded p-2 px-3">
                        <div className="fs-4 fw-bold">45</div>
                        <div className="small">Mins</div>
                      </div>
                      <div className="bg-white text-dark rounded p-2 px-3">
                        <div className="fs-4 fw-bold">30</div>
                        <div className="small">Secs</div>
                      </div>
                    </div>
                    <Link to="/products" className="btn btn-light btn-lg mx-auto" style={{width: 'fit-content'}}>
                      <i className="fas fa-shopping-cart me-2"></i>Shop Now
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 mb-4">
              <div className="card border-0 shadow-lg rounded-lg overflow-hidden offer-card">
                <div className="position-relative">
                  <img
                    src={require("../images/banner images/new arrival.jpg")}
                    className="card-img"
                    alt="New Arrivals"
                    style={{height: '300px', objectFit: 'cover', objectPosition: 'center'}}
                    onError={(e) => e.target.src = "https://via.placeholder.com/600x300?text=New+Arrivals"}
                  />
                  <div className="position-absolute top-0 start-0 w-100 h-100 bg-primary" style={{opacity: '0.7'}}></div>
                  <div className="card-img-overlay d-flex flex-column justify-content-center text-center">
                    <div className="bg-success text-white d-inline-block py-2 px-4 rounded-pill mx-auto mb-3">Just Arrived</div>
                    <h3 className="card-title display-6 fw-bold text-white mb-3">New Collection</h3>
                    <p className="card-text text-white fs-5 mb-4">Discover the latest trends and styles</p>
                    <div className="d-flex justify-content-center gap-3 mb-4">
                      <div className="bg-white rounded-circle p-2" style={{width: '60px', height: '60px'}}>
                        <img src={require("../images/product images/product-1.jpg")} className="img-fluid rounded-circle" alt="Product"
                             onError={(e) => e.target.src = "https://via.placeholder.com/60x60"} />
                      </div>
                      <div className="bg-white rounded-circle p-2" style={{width: '60px', height: '60px'}}>
                        <img src={require("../images/product images/product-2.jpg")} className="img-fluid rounded-circle" alt="Product"
                             onError={(e) => e.target.src = "https://via.placeholder.com/60x60"} />
                      </div>
                      <div className="bg-white rounded-circle p-2" style={{width: '60px', height: '60px'}}>
                        <img src={require("../images/product images/product-3.JPG")} className="img-fluid rounded-circle" alt="Product"
                             onError={(e) => e.target.src = "https://via.placeholder.com/60x60"} />
                      </div>
                    </div>
                    <Link to="/products" className="btn btn-light btn-lg mx-auto" style={{width: 'fit-content'}}>
                      <i className="fas fa-eye me-2"></i>View Collection
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for offer cards */}
      <style jsx="true">{`
        .offer-card {
          transition: all 0.3s ease;
        }
        .offer-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 30px rgba(0,0,0,0.2) !important;
        }
      `}</style>
    </div>
  );
};

export default HomePage;
