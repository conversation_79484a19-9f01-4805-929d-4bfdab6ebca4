import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import orders from '../data/orders';

const OrdersPage = () => {
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [filterStatus, setFilterStatus] = useState('All');

  // Filter orders by status
  const filteredOrders = filterStatus === 'All' 
    ? orders 
    : orders.filter(order => order.status === filterStatus);

  // Format date
  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch(status) {
      case 'Processing':
        return 'bg-warning';
      case 'Shipped':
        return 'bg-info';
      case 'Delivered':
        return 'bg-success';
      case 'Cancelled':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  };

  return (
    <div className="container py-5">
      <h1 className="mb-4">My Orders</h1>
      
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="d-flex align-items-center">
            <span className="me-3">Filter by status:</span>
            <select 
              className="form-select w-auto" 
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="All">All Orders</option>
              <option value="Processing">Processing</option>
              <option value="Shipped">Shipped</option>
              <option value="Delivered">Delivered</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>
        </div>
        <div className="col-md-6 text-md-end">
          <span className="text-muted">{filteredOrders.length} order(s) found</span>
        </div>
      </div>

      {filteredOrders.length > 0 ? (
        <div className="row">
          <div className="col-md-5">
            <div className="list-group">
              {filteredOrders.map(order => (
                <button
                  key={order.id}
                  className={`list-group-item list-group-item-action ${selectedOrder?.id === order.id ? 'active' : ''}`}
                  onClick={() => setSelectedOrder(order)}
                >
                  <div className="d-flex w-100 justify-content-between">
                    <h5 className="mb-1">{order.id}</h5>
                    <small>{formatDate(order.date)}</small>
                  </div>
                  <p className="mb-1">
                    {order.items.reduce((total, item) => total + item.quantity, 0)} item(s) - ${order.total.toFixed(2)}
                  </p>
                  <div className="d-flex justify-content-between align-items-center">
                    <small>Payment: {order.payment.method === 'credit' ? 'Credit Card' : 
                                    order.payment.method === 'paypal' ? 'PayPal' : 'Cash on Delivery'}</small>
                    <span className={`badge ${getStatusBadgeClass(order.status)}`}>{order.status}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
          
          <div className="col-md-7">
            {selectedOrder ? (
              <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Order Details - {selectedOrder.id}</h5>
                  <span className={`badge ${getStatusBadgeClass(selectedOrder.status)}`}>{selectedOrder.status}</span>
                </div>
                <div className="card-body">
                  <div className="mb-4">
                    <h6 className="card-subtitle mb-2 text-muted">Order Date</h6>
                    <p>{formatDate(selectedOrder.date)}</p>
                  </div>
                  
                  <div className="mb-4">
                    <h6 className="card-subtitle mb-2 text-muted">Shipping Address</h6>
                    <p className="mb-1">{selectedOrder.customer.name}</p>
                    <p className="mb-1">{selectedOrder.customer.address}</p>
                    <p className="mb-1">
                      {selectedOrder.customer.city}, {selectedOrder.customer.state} {selectedOrder.customer.zipCode}
                    </p>
                    <p className="mb-1">{selectedOrder.customer.country}</p>
                  </div>
                  
                  <div className="mb-4">
                    <h6 className="card-subtitle mb-2 text-muted">Payment Method</h6>
                    <p>
                      {selectedOrder.payment.method === 'credit' ? (
                        <>Credit Card ending in {selectedOrder.payment.cardNumber.slice(-4)}</>
                      ) : selectedOrder.payment.method === 'paypal' ? (
                        <>PayPal</>
                      ) : (
                        <>Cash on Delivery</>
                      )}
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <h6 className="card-subtitle mb-2 text-muted">Order Items</h6>
                    <div className="list-group">
                      {selectedOrder.items.map(item => (
                        <div key={item.id} className="list-group-item">
                          <div className="d-flex">
                            <div style={{width: '60px', height: '60px', overflow: 'hidden'}}>
                              <img 
                                src={item.image} 
                                alt={item.name} 
                                className="img-fluid rounded"
                                style={{objectFit: 'cover', height: '100%', width: '100%'}}
                                onError={(e) => e.target.src = `https://via.placeholder.com/60x60?text=${encodeURIComponent(item.name)}`}
                              />
                            </div>
                            <div className="ms-3 flex-grow-1">
                              <div className="d-flex justify-content-between">
                                <h6 className="mb-0">{item.name}</h6>
                                <span>${(item.price * item.quantity).toFixed(2)}</span>
                              </div>
                              <small className="text-muted">
                                ${item.price.toFixed(2)} x {item.quantity}
                              </small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h6 className="card-subtitle mb-2 text-muted">Order Summary</h6>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Subtotal</span>
                      <span>${selectedOrder.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Shipping</span>
                      <span>{selectedOrder.shipping === 0 ? 'Free' : `$${selectedOrder.shipping.toFixed(2)}`}</span>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Tax</span>
                      <span>${selectedOrder.tax.toFixed(2)}</span>
                    </div>
                    <hr />
                    <div className="d-flex justify-content-between">
                      <strong>Total</strong>
                      <strong>${selectedOrder.total.toFixed(2)}</strong>
                    </div>
                  </div>
                  
                  <div className="d-flex justify-content-between">
                    <Link to="/products" className="btn btn-outline-primary">
                      <i className="fas fa-shopping-cart me-2"></i>Continue Shopping
                    </Link>
                    <button className="btn btn-primary">
                      <i className="fas fa-print me-2"></i>Print Receipt
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="card">
                <div className="card-body text-center py-5">
                  <i className="fas fa-shopping-bag fa-4x mb-3 text-muted"></i>
                  <h5>Select an order to view details</h5>
                  <p className="text-muted">Click on an order from the list to view its details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="card-body text-center py-5">
            <i className="fas fa-shopping-bag fa-4x mb-3 text-muted"></i>
            <h3>No orders found</h3>
            <p className="text-muted">You haven't placed any orders yet or no orders match your filter.</p>
            <Link to="/products" className="btn btn-primary mt-3">
              <i className="fas fa-shopping-cart me-2"></i>Start Shopping
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersPage;
