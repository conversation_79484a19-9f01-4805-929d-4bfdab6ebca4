import React, { useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import products from '../data/products';
import categories from '../data/categories';
import { CartContext } from '../context/CartContext';

const ProductsPage = () => {
  const { addToCart } = useContext(CartContext);

  // Handle add to cart with animation
  const handleAddToCart = (e, product) => {
    // Add the product to cart
    addToCart(product, 1);

    // Store a reference to the button element
    const button = e.currentTarget;

    // Add animation class
    if (button && button.classList) {
      button.classList.add('add-to-cart-animation');

      // Remove animation class after animation completes
      setTimeout(() => {
        // Check if the element still exists in the DOM
        if (button && button.classList && document.body.contains(button)) {
          button.classList.remove('add-to-cart-animation');
        }
      }, 500);
    }
  };
  const [filteredProducts, setFilteredProducts] = useState(products);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('default');
  const [searchTerm, setSearchTerm] = useState('');

  // Handle category filter
  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    if (category === 'All') {
      setFilteredProducts(products);
    } else {
      setFilteredProducts(products.filter(product => product.category === category));
    }
  };

  // Handle sort
  const handleSort = (e) => {
    const value = e.target.value;
    setSortBy(value);

    let sortedProducts = [...filteredProducts];

    switch (value) {
      case 'price-low-high':
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-high-low':
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        sortedProducts.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // Default sorting (by id)
        sortedProducts.sort((a, b) => a.id - b.id);
    }

    setFilteredProducts(sortedProducts);
  };

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value === '') {
      // If search is cleared, reset to category filter
      handleCategoryFilter(selectedCategory);
    } else {
      // Filter by search term within the current category filter
      let productsToSearch = selectedCategory === 'All'
        ? products
        : products.filter(product => product.category === selectedCategory);

      setFilteredProducts(
        productsToSearch.filter(product =>
          product.name.toLowerCase().includes(value.toLowerCase()) ||
          product.description.toLowerCase().includes(value.toLowerCase())
        )
      );
    }
  };

  return (
    <div className="container py-5">
      <h1 className="mb-4">Products</h1>

      {/* Animation styles */}
      <style jsx="true">{`
        .add-to-cart-animation {
          animation: pulse 0.5s ease;
        }
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.2); }
          100% { transform: scale(1); }
        }
      `}</style>

      {/* Search and Filter Row */}
      <div className="row mb-4">
        <div className="col-md-6 mb-3">
          <input
            type="text"
            className="form-control"
            placeholder="Search products..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="col-md-3 mb-3">
          <select className="form-select" value={sortBy} onChange={handleSort}>
            <option value="default">Sort by: Default</option>
            <option value="price-low-high">Price: Low to High</option>
            <option value="price-high-low">Price: High to Low</option>
            <option value="rating">Rating</option>
          </select>
        </div>
        <div className="col-md-3 mb-3">
          <select
            className="form-select"
            value={selectedCategory}
            onChange={(e) => handleCategoryFilter(e.target.value)}
          >
            <option value="All">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.name}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Products Grid */}
      <div className="row">
        {filteredProducts.length > 0 ? (
          filteredProducts.map(product => (
            <div key={product.id} className="col-md-3 mb-4">
              <div className="card h-100">
                <div style={{height: '200px', overflow: 'hidden'}}>
                  <img
                    src={product.image}
                    className="card-img-top"
                    alt={product.name}
                    style={{objectFit: 'cover', height: '100%', width: '100%', objectPosition: 'center'}}
                    onError={(e) => e.target.src = `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}`}
                  />
                </div>
                <div className="card-body">
                  <h5 className="card-title">{product.name}</h5>
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span className="text-muted">${product.price.toFixed(2)}</span>
                    <span className="badge bg-primary">
                      <i className="fas fa-star"></i> {product.rating}
                    </span>
                  </div>
                  <p className="card-text text-truncate">{product.description}</p>
                  <div className="d-flex justify-content-between">
                    <Link to={`/products/${product.id}`} className="btn btn-outline-primary">Details</Link>
                    <button
                      className="btn btn-primary"
                      onClick={(e) => handleAddToCart(e, product)}
                    >
                      <i className="fas fa-shopping-cart"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-12 text-center py-5">
            <h3>No products found</h3>
            <p>Try changing your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductsPage;
